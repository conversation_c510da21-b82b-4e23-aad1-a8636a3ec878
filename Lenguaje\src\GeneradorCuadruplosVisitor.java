import java.util.*;

public class GeneradorCuadruplosVisitor extends LenguajeBaseVisitor<String> {

    // Pilas para operadores, operandos y tipos
    private Stack<String> operatorStack = new Stack<>();
    private Stack<String> operandStack = new Stack<>();
    private Stack<String> typeStack = new Stack<>();

    // Pilas para saltos (para condicionales y ciclos)
    private Stack<Integer> jumpStack = new Stack<>();

    // Pila para direcciones de funciones
    private Stack<Integer> functionStack = new Stack<>();

    // Mapa para almacenar las direcciones de inicio de las funciones
    private Map<String, Integer> functionAddresses = new HashMap<>();

    // Lista para almacenar los cuádruplos generados
    private List<Quadruple> quadruples = new ArrayList<>();

    // Contador para variables temporales
    private int tempVarCounter = 0;

    // Contador para índice de cuádruplos
    private int quadCounter = 0;

    // Nota: Variables 'function' y 'symbolTable' removidas - no se usaban en el código

    // Pila de tablas de símbolos para manejar ámbitos (similar al SemanticVisitor)
    private Stack<Map<String, SemanticData.Variable>> symbolTableStack = new Stack<>();

    // Administrador de memoria para direcciones virtuales
    private VirtualMemory memory = new VirtualMemory();

    // Mapa para almacenar la relación entre operandos y direcciones
    private Map<String, Integer> operandToAddress = new HashMap<>();

    // Mapa para rastrear constantes y sus valores
    private Map<Integer, Object> constantValues = new HashMap<>();

    /**
     * Clase que representa un cuádruplo con direcciones virtuales.
     * Cada cuádruplo contiene un operador y hasta tres operandos (direcciones virtuales).
     */
    public static class Quadruple {
        String operator;  // Operador
        int arg1;         // Primer argumento (dirección virtual)
        int arg2;         // Segundo argumento (dirección virtual)
        int result;       // Resultado (dirección virtual)

        /**
         * Constructor para operaciones con dos operandos.
         * @param operator Operador
         * @param arg1 Dirección virtual del primer operando
         * @param arg2 Dirección virtual del segundo operando
         * @param result Dirección virtual del resultado
         */
        public Quadruple(String operator, int arg1, int arg2, int result) {
            this.operator = operator;
            this.arg1 = arg1;
            this.arg2 = arg2;
            this.result = result;
        }

        /**
         * Constructor para operaciones con un operando (como PRINT).
         * @param operator Operador
         * @param arg1 Dirección virtual del operando
         */
        public Quadruple(String operator, int arg1) {
            this.operator = operator;
            this.arg1 = arg1;
            this.arg2 = -1;
            this.result = -1;
        }

        /**
         * Constructor para saltos (GOTO, GOTOF, GOTOT).
         * @param operator Operador de salto
         * @param arg1 Dirección virtual de la condición (para GOTOF/GOTOT) o -1 (para GOTO)
         * @param result Índice del cuádruplo destino
         */
        public Quadruple(String operator, int arg1, int result) {
            this.operator = operator;
            this.arg1 = arg1;
            this.arg2 = -1;
            this.result = result;
        }

        /**
         * Constructor para operaciones con funciones (ERA, GOSUB).
         * @param operator Operador de función
         * @param functionName Nombre de la función (se almacena como string en arg1)
         * @param arg2 Segundo argumento (generalmente -1)
         * @param result Resultado (generalmente -1)
         */
        public Quadruple(String operator, String functionName, int arg2, int result) {
            this.operator = operator;
            this.arg1 = functionName.hashCode(); // Convertimos el nombre a un hash para almacenarlo
            this.arg2 = arg2;
            this.result = result;
        }

        @Override
        public String toString() {
            if (operator.equals("GOTO")) {
                return operator + " - - " + result;
            } else if (operator.equals("GOTOF") || operator.equals("GOTOT")) {
                return operator + " " + arg1 + " - " + result;
            } else if (operator.equals("PRINT")) {
                return operator + " " + arg1 + " - -";
            } else if (operator.equals("=")) {
                return operator + " " + arg1 + " - " + result;
            } else if (operator.equals("ERA")) {
                return operator + " " + arg1 + " - -";
            } else if (operator.equals("PARAM")) {
                return operator + " " + arg1 + " - " + result;
            } else if (operator.equals("GOSUB")) {
                return operator + " " + arg1 + " - " + result;
            } else if (operator.equals("ENDFunc")) {
                return operator + " - - -";
            } else if (operator.equals("RETURN")) {
                return operator + " " + arg1 + " - -";
            } else {
                return operator + " " + arg1 + " " + arg2 + " " + result;
            }
        }
    }

    /**
     * Constructor que recibe la tabla de símbolos.
     * @param globalSymbolTable Tabla de símbolos con las variables declaradas
     */
    public GeneradorCuadruplosVisitor(Map<String, SemanticData.Variable> globalSymbolTable) {
        // Inicializar la pila con la tabla global
        symbolTableStack.push(globalSymbolTable);
    }

    /**
     * Genera un nombre para una variable temporal.
     * @return Nombre de la variable temporal
     */
    private String generateTempVar() {
        return "t" + tempVarCounter++;
    }

    /**
     * Busca una variable en la pila de tablas de símbolos.
     * @param variableName Nombre de la variable a buscar
     * @return Variable encontrada o null si no existe
     */
    private SemanticData.Variable findVariable(String variableName) {
        // Buscar desde la tabla más local (tope de la pila) hacia la global
        for (int i = symbolTableStack.size() - 1; i >= 0; i--) {
            Map<String, SemanticData.Variable> table = symbolTableStack.get(i);
            if (table.containsKey(variableName)) {
                return table.get(variableName);
            }
        }
        return null;
    }

    /**
     * Determina el tipo de un operando.
     * @param operand Operando a evaluar
     * @return Tipo del operando (int, float, str, bool)
     */
    private String getType(String operand) {
        if (operand.matches("\\d+")) {
            return "int";
        } else if (operand.matches("\\d+\\.\\d+")) {
            return "float";
        } else if (operand.startsWith("\"") && operand.endsWith("\"")) {
            return "str";
        } else if (operand.equals("true") || operand.equals("false")) {
            return "bool";
        } else if (operand.startsWith("t")) {
            // Para temporales, verificar si está en la pila de tipos
            if (!typeStack.isEmpty()) {
                return typeStack.peek();
            } else {
                // Si no está en la pila, intentar inferir el tipo por el nombre
                // (esto es una simplificación)
                if (operandToAddress.containsKey(operand)) {
                    int address = operandToAddress.get(operand);
                    if (address >= VirtualMemory.TEMP_INT_START && address < VirtualMemory.TEMP_FLOAT_START) {
                        return "int";
                    } else if (address >= VirtualMemory.TEMP_FLOAT_START && address < VirtualMemory.TEMP_STRING_START) {
                        return "float";
                    } else if (address >= VirtualMemory.TEMP_STRING_START && address < VirtualMemory.TEMP_BOOL_START) {
                        return "str";
                    } else if (address >= VirtualMemory.TEMP_BOOL_START && address < VirtualMemory.CTE_INT_START) {
                        return "bool";
                    }
                }
                return "unknown";
            }
        } else {
            // Buscar la variable en la pila de tablas de símbolos
            SemanticData.Variable variable = findVariable(operand);
            if (variable != null) {
                return variable.tipo;
            } else {
                return "unknown";
            }
        }
    }

    /**
     * Obtiene o crea una dirección virtual para un operando.
     * @param operand Operando para el cual se necesita una dirección
     * @param type Tipo del operando
     * @return Dirección virtual asignada
     */
    private int getAddressForOperand(String operand, String type) {
        if (operandToAddress.containsKey(operand)) {
            return operandToAddress.get(operand);
        }

        int address;
        if (operand.matches("\\d+")) {
            // Constante entera
            address = memory.getConstantAddress(operand, "int");
            constantValues.put(address, Integer.parseInt(operand));
        } else if (operand.matches("\\d+\\.\\d+")) {
            // Constante flotante
            address = memory.getConstantAddress(operand, "float");
            constantValues.put(address, Float.parseFloat(operand));
        } else if (operand.startsWith("\"") && operand.endsWith("\"")) {
            // Constante string
            address = memory.getConstantAddress(operand, "str");
            // Remover las comillas de la string
            String stringValue = operand.substring(1, operand.length() - 1);
            constantValues.put(address, stringValue);
        } else if (operand.startsWith("t")) {
            // Variable temporal
            address = memory.getTempAddressForName(operand, type);
        } else {
            // Variable
            boolean isGlobal = true; // Simplificado, en realidad deberías verificar el ámbito
            address = memory.getVariableAddress(operand, type, isGlobal);
        }

        operandToAddress.put(operand, address);
        return address;
    }

    //  ---  Visitor Methods  ---

    @Override
    public String visitProgram(LenguajeParser.ProgramContext ctx) {
        System.out.println("DEBUG: Visitando programa...");

        System.out.println("DEBUG: Procesando variables globales...");
        visit(ctx.vars_opt());

        System.out.println("DEBUG: Procesando funciones...");
        visit(ctx.funcs_p());

        System.out.println("DEBUG: Procesando main...");
        visit(ctx.body());

        return null;
    }

    /**
     * Visita un nodo de asignación y genera los cuádruplos correspondientes.
     * Implementa la traducción de estatutos de asignación a direcciones virtuales.
     */
    @Override
    public String visitAssing(LenguajeParser.AssingContext ctx) {
        String varName = ctx.ID().getText();
        visit(ctx.expresion()); // Procesar la expresión

        // Obtener el resultado de la expresión
        String resultExpr = operandStack.pop();
        String typeExpr = typeStack.pop();

        // Obtener el tipo de la variable usando la búsqueda en la pila de tablas
        String typeVar;
        SemanticData.Variable variable = findVariable(varName);
        if (variable != null) {
            typeVar = variable.tipo;
        } else {
            // Si la variable no está en ninguna tabla de símbolos, inferimos su tipo
            // basándonos en el tipo de la expresión
            typeVar = typeExpr;
            System.out.println("Advertencia: Variable '" + varName + "' no encontrada en la tabla de símbolos. Usando tipo: " + typeVar);
        }

        // Obtener direcciones virtuales
        int addressResult = getAddressForOperand(resultExpr, typeExpr);
        int addressVar = getAddressForOperand(varName, typeVar);

        // Generar cuádruplo de asignación
        Quadruple quad = new Quadruple("=", addressResult, -1, addressVar);
        quadruples.add(quad);
        System.out.println("DEBUG QUAD GENERADO: " + quadCounter + ": " + quad);
        quadCounter++;

        return null;
    }

    /**
     * Visita las declaraciones de funciones y genera los cuádruplos correspondientes.
     * Implementa la traducción de declaraciones de funciones a direcciones virtuales.
     */
    @Override
    public String visitFuncs_p(LenguajeParser.Funcs_pContext ctx) {
        // Procesar cada declaración de función
        if (ctx.funcs() != null) {
            visit(ctx.funcs());
        }
        // Procesar recursivamente más funciones
        if (ctx.funcs_p() != null) {
            visit(ctx.funcs_p());
        }
        return null;
    }

    /**
     * Visita una declaración de función individual y genera los cuádruplos correspondientes.
     * Implementa la traducción de declaraciones de funciones a direcciones virtuales.
     */
    @Override
    public String visitFuncs(LenguajeParser.FuncsContext ctx) {
        String funcName = ctx.ID().getText();

        // Generar GOTO para saltar el código de la función durante la ejecución secuencial
        Quadruple gotoQuad = new Quadruple("GOTO", -1);
        quadruples.add(gotoQuad);
        System.out.println("DEBUG QUAD GENERADO: " + quadCounter + ": " + gotoQuad);
        int skipFunctionJump = quadCounter;
        quadCounter++;

        // Guardar la dirección de inicio de la función
        functionAddresses.put(funcName, quadCounter);

        // Crear nueva tabla de símbolos local para la función
        Map<String, SemanticData.Variable> localSymbolTable = new HashMap<>();
        symbolTableStack.push(localSymbolTable);

        // Procesar parámetros de la función (atributo)
        if (ctx.atributo() != null) {
            visit(ctx.atributo());
        }

        // Procesar variables locales de la función (si las hay)
        if (ctx.vars_opt() != null) {
            visit(ctx.vars_opt());
        }

        // Procesar el cuerpo de la función
        visit(ctx.body());

        // Generar cuádruplo ENDFUNC al final de la función
        quadruples.add(new Quadruple("ENDFUNC", -1, -1, -1));
        quadCounter++;

        // Salir del ámbito local de la función
        symbolTableStack.pop();

        // Completar el GOTO para saltar la función
        quadruples.get(skipFunctionJump).result = quadCounter;

        return null;
    }

    /**
     * Visita los parámetros de una función y los agrega a la tabla de símbolos local.
     */
    @Override
    public String visitAtributo(LenguajeParser.AtributoContext ctx) {
        if (ctx.ID() != null) {
            String paramName = ctx.ID().getText();
            String paramType = ctx.type().getText();

            // Agregar el parámetro a la tabla de símbolos local (tope de la pila)
            Map<String, SemanticData.Variable> localTable = symbolTableStack.peek();
            localTable.put(paramName, new SemanticData.Variable(paramName, paramType));

            // Procesar más parámetros si los hay
            if (ctx.atr_opt() != null) {
                visit(ctx.atr_opt());
            }
        }
        return null;
    }

    /**
     * Visita parámetros adicionales de una función.
     */
    @Override
    public String visitAtr_opt(LenguajeParser.Atr_optContext ctx) {
        if (ctx.COMMA() != null && ctx.atributo() != null) {
            visit(ctx.atributo());
        }
        return null;
    }

    /**
     * Visita declaraciones de variables y las agrega a la tabla de símbolos actual.
     */
    @Override
    public String visitVars(LenguajeParser.VarsContext ctx) {
        String type = ctx.type().getText();

        // Obtener la tabla de símbolos actual (tope de la pila)
        Map<String, SemanticData.Variable> currentTable = symbolTableStack.peek();

        // Agregar cada variable a la tabla actual
        for (int i = 0; i < ctx.id_list().ID().size(); i++) {
            String varName = ctx.id_list().ID(i).getText();
            currentTable.put(varName, new SemanticData.Variable(varName, type));
        }

        return null;
    }

    /**
     * Visita el cuerpo de un bloque (body) y procesa la lista de statements.
     * Implementa la traducción de bloques de código a cuádruplos.
     */
    @Override
    public String visitBody(LenguajeParser.BodyContext ctx) {
        // Procesar la lista de statements dentro del cuerpo
        if (ctx.statement_list() != null) {
            visit(ctx.statement_list());
        }
        return null;
    }

    /**
     * Visita una lista de statements y procesa cada uno.
     * Implementa la traducción de listas de statements a cuádruplos.
     */
    @Override
    public String visitStatement_list(LenguajeParser.Statement_listContext ctx) {
        // Procesar cada statement en la lista
        if (ctx.statement() != null) {
            for (LenguajeParser.StatementContext statementCtx : ctx.statement()) {
                visit(statementCtx);
            }
        }
        return null;
    }

    /**
     * Visita un statement individual y lo procesa según su tipo.
     * Implementa la traducción de statements individuales a cuádruplos.
     */
    @Override
    public String visitStatement(LenguajeParser.StatementContext ctx) {
        // Agregar debugging para ver qué tipo de statement se está procesando
        System.out.println("DEBUG: Visitando statement...");
        if (ctx.assing() != null) {
            System.out.println("DEBUG: Es una asignación");
            visit(ctx.assing());
        } else if (ctx.condition() != null) {
            System.out.println("DEBUG: Es una condición");
            visit(ctx.condition());
        } else if (ctx.cycle() != null) {
            System.out.println("DEBUG: Es un ciclo - ¡ESTO NO DEBERÍA PASAR!");
            visit(ctx.cycle());
        } else if (ctx.f_call() != null) {
            System.out.println("DEBUG: Es una llamada a función");
            visit(ctx.f_call());
        } else if (ctx.print() != null) {
            System.out.println("DEBUG: Es un print");
            visit(ctx.print());
        }
        return null;
    }

    /**
     * Visita un nodo de condición (if-else) y genera los cuádruplos correspondientes.
     * Implementa la traducción de estatutos condicionales a direcciones virtuales.
     */
    @Override
    public String visitCondition(LenguajeParser.ConditionContext ctx) {
        // Procesar la condición
        visit(ctx.expresion());
        String condition = operandStack.pop();
        String typeCondition = typeStack.pop();

        // Obtener dirección virtual para la condición
        int addressCondition = getAddressForOperand(condition, typeCondition);

        // Generar GOTOF (salto si falso)
        quadruples.add(new Quadruple("GOTOF", addressCondition, -1));
        int gotoFPos = quadCounter;
        quadCounter++;

        // Guardar posición en la pila de saltos
        jumpStack.push(gotoFPos);

        // Procesar el cuerpo del if
        visit(ctx.body(0));

        // Si hay un else
        if (ctx.ELSE() != null) {
            // Generar GOTO para saltar el else después del if
            quadruples.add(new Quadruple("GOTO", -1));
            int gotoPos = quadCounter;
            quadCounter++;

            // Completar el GOTOF para saltar al else
            int falseJump = jumpStack.pop();
            quadruples.get(falseJump).result = quadCounter;

            // Guardar posición del GOTO en la pila de saltos
            jumpStack.push(gotoPos);

            // Procesar el cuerpo del else
            visit(ctx.body(1));

            // Completar el GOTO para saltar después del else
            int endJump = jumpStack.pop();
            quadruples.get(endJump).result = quadCounter;
        } else {
            // Completar el GOTOF para saltar después del if
            int falseJump = jumpStack.pop();
            quadruples.get(falseJump).result = quadCounter;
        }

        return null;
    }

    /**
     * Visita un nodo de ciclo (while) y genera los cuádruplos correspondientes.
     * Implementa la traducción de estatutos cíclicos a direcciones virtuales.
     */
    @Override
    public String visitCycle(LenguajeParser.CycleContext ctx) {
        // Guardar la posición de inicio del ciclo
        int startPos = quadCounter;

        // Procesar la condición
        visit(ctx.expresion());
        String condition = operandStack.pop();
        String typeCondition = typeStack.pop();

        // Obtener dirección virtual para la condición
        int addressCondition = getAddressForOperand(condition, typeCondition);

        // Generar GOTOF (salto si falso)
        quadruples.add(new Quadruple("GOTOF", addressCondition, -1));
        int gotoFPos = quadCounter;
        quadCounter++;

        // Guardar posición en la pila de saltos
        jumpStack.push(gotoFPos);

        // Procesar el cuerpo del ciclo
        visit(ctx.body());

        // Generar GOTO para volver al inicio del ciclo
        quadruples.add(new Quadruple("GOTO", -1, startPos));
        quadCounter++;

        // Completar el GOTOF para saltar después del ciclo
        int exitJump = jumpStack.pop();
        quadruples.get(exitJump).result = quadCounter;

        return null;
    }

    /**
     * Visita un nodo de impresión (print) y genera los cuádruplos correspondientes.
     * Implementa la traducción de estatutos de impresión a direcciones virtuales.
     */
    @Override
    public String visitPrint(LenguajeParser.PrintContext ctx) {
        visit(ctx.expresion_list());

        // Procesar cada expresión en la lista (en orden inverso debido a la pila)
        List<String> expressions = new ArrayList<>();
        List<String> types = new ArrayList<>();

        while (!operandStack.isEmpty()) {
            expressions.add(operandStack.pop());
            types.add(typeStack.pop());
        }

        // Generar cuádruplos PRINT para cada expresión (en orden correcto)
        for (int i = expressions.size() - 1; i >= 0; i--) {
            // Obtener dirección virtual para la expresión
            int addressExpr = getAddressForOperand(expressions.get(i), types.get(i));

            // Generar cuádruplo PRINT
            Quadruple printQuad = new Quadruple("PRINT", addressExpr);
            quadruples.add(printQuad);
            System.out.println("DEBUG QUAD GENERADO: " + quadCounter + ": " + printQuad);
            quadCounter++;
        }

        return null;
    }

    /**
     * Visita un nodo de llamada a función (f_call) y genera los cuádruplos correspondientes.
     * Implementa la traducción de llamadas a funciones a direcciones virtuales.
     */
    @Override
    public String visitF_call(LenguajeParser.F_callContext ctx){
        String funcName = ctx.ID().getText();

        // Procesar argumentos antes de generar ERA
        visit(ctx.expresion_list_opt());

        // Generar cuádruplo ERA
        // Usamos el hash del nombre de la función como identificador
        quadruples.add(new Quadruple("ERA", funcName.hashCode(), -1, -1));
        quadCounter++;

        // Procesar cada argumento (en orden inverso debido a la pila)
        List<String> arguments = new ArrayList<>();
        List<String> types = new ArrayList<>();

        while (!operandStack.isEmpty()) {
            arguments.add(operandStack.pop());
            types.add(typeStack.pop());
        }

        // Generar cuádruplos PARAM para cada argumento (en orden correcto)
        for (int i = arguments.size() - 1; i >= 0; i--) {
            // Obtener dirección virtual para el argumento
            int addressArg = getAddressForOperand(arguments.get(i), types.get(i));

            // Generar cuádruplo PARAM (argumento, -, posición del parámetro)
            quadruples.add(new Quadruple("PARAM", addressArg, -1, i));
            quadCounter++;
        }

        // Generar cuádruplo GOSUB con la dirección de la función
        int funcAddress = functionAddresses.getOrDefault(funcName, -1);
        quadruples.add(new Quadruple("GOSUB", funcAddress, -1, -1));
        quadCounter++;

        return null;
    }

    @Override
    public String visitExpresion(LenguajeParser.ExpresionContext ctx) {
        visit(ctx.andExpr(0));

        for (int i = 1; i < ctx.andExpr().size(); i++) {
            operatorStack.push("||");
            visit(ctx.andExpr(i));
            generateQuadruple();
        }
        return null;
    }

    @Override
    public String visitAndExpr(LenguajeParser.AndExprContext ctx) {
        visit(ctx.comparacion(0));

        for (int i = 1; i < ctx.comparacion().size(); i++) {
            operatorStack.push("&&");
            visit(ctx.comparacion(i));
            generateQuadruple();
        }
        return null;
    }

    @Override
    public String visitComparacion(LenguajeParser.ComparacionContext ctx) {
        visit(ctx.aritmetica(0));

        if (ctx.getChildCount() > 1) {
            operatorStack.push(ctx.getChild(1).getText()); // Push the comparison operator
            visit(ctx.aritmetica(1));
            generateQuadruple();
        }
        return null;
    }

    @Override
    public String visitAritmetica(LenguajeParser.AritmeticaContext ctx) {
        visit(ctx.termino(0));

        for (int i = 1; i < ctx.termino().size(); i++) {
            operatorStack.push(ctx.getChild(i * 2 - 1).getText()); // Push + or -
            visit(ctx.termino(i));
            generateQuadruple();
        }
        return null;
    }

    @Override
    public String visitTermino(LenguajeParser.TerminoContext ctx) {
        visit(ctx.factor(0));

        for (int i = 1; i < ctx.factor().size(); i++) {
            operatorStack.push(ctx.getChild(i * 2 - 1).getText()); // Push * or /
            visit(ctx.factor(i));
            generateQuadruple();
        }
        return null;
    }

    @Override
    public String visitFactor(LenguajeParser.FactorContext ctx) {
        if (ctx.LPAREN() != null) {
            return visitExpresion(ctx.expresion()); // Visit the expression inside parentheses
        } else if (ctx.ID() != null) {
            String id = ctx.ID().getText();
            String type = getType(id);
            operandStack.push(id);
            typeStack.push(type);
            return id;
        } else if (ctx.cte() != null) {
            visitCte(ctx.cte());
            return operandStack.peek();
        } else if (ctx.PLUS() != null || ctx.MINUS() != null) {
            // Unary plus/minus
            if (ctx.ID() != null) {
                String id = ctx.ID().getText();
                String type = getType(id);

                if (ctx.MINUS() != null) {
                    // Para el caso de -ID, generamos un temporal con el valor negado
                    String tempVar = generateTempVar();
                    int addressId = getAddressForOperand(id, type);
                    int addressTemp = memory.getTempAddressForName(tempVar, type);

                    // Generar cuádruplo para la negación
                    quadruples.add(new Quadruple("*", addressId,
                                  getAddressForOperand("-1", "int"), addressTemp));
                    quadCounter++;

                    operandStack.push(tempVar);
                    typeStack.push(type);
                    return tempVar;
                } else {
                    // Para +ID, simplemente usamos el ID
                    operandStack.push(id);
                    typeStack.push(type);
                    return id;
                }
            } else if (ctx.cte() != null) {
                visitCte(ctx.cte());
                String cte = operandStack.pop();
                String type = typeStack.pop();

                if (ctx.MINUS() != null) {
                    // Para -CTE, generamos un temporal con el valor negado
                    String tempVar = generateTempVar();
                    int addressCte = getAddressForOperand(cte, type);
                    int addressTemp = memory.getTempAddressForName(tempVar, type);

                    // Generar cuádruplo para la negación
                    quadruples.add(new Quadruple("*", addressCte,
                                  getAddressForOperand("-1", "int"), addressTemp));
                    quadCounter++;

                    operandStack.push(tempVar);
                    typeStack.push(type);
                    return tempVar;
                } else {
                    // Para +CTE, simplemente usamos la CTE
                    operandStack.push(cte);
                    typeStack.push(type);
                    return cte;
                }
            }
        }
        return null;
    }

    @Override
    public String visitCte(LenguajeParser.CteContext ctx) {
        if (ctx.CTE_INT() != null) {
            operandStack.push(ctx.CTE_INT().getText());
            typeStack.push("int");
        } else if (ctx.CTE_FLOAT() != null) {
            operandStack.push(ctx.CTE_FLOAT().getText());
            typeStack.push("float");
        } else if (ctx.CTE_STRING() != null) {
            operandStack.push(ctx.CTE_STRING().getText());
            typeStack.push("str");
        }
        return null;
    }

    // --- Generación de Cuádruplos ---

    /**
     * Genera un cuádruplo para la operación en la cima de la pila de operadores.
     * Utiliza los dos operandos superiores de la pila de operandos.
     */
    private void generateQuadruple() {
        if (!operatorStack.isEmpty()) {
            String operator = operatorStack.pop();
            String operand2 = operandStack.pop();
            String operand1 = operandStack.pop();
            String type2 = typeStack.pop();
            String type1 = typeStack.pop();

            // Determinar el tipo resultante (simplificado)
            String resultType = determineResultType(type1, operator, type2);

            // Generar variable temporal para el resultado
            String tempVar = generateTempVar();

            // Obtener direcciones virtuales para los operandos
            int address1 = getAddressForOperand(operand1, type1);
            int address2 = getAddressForOperand(operand2, type2);
            int addressTemp = memory.getTempAddressForName(tempVar, resultType);

            // Agregar el cuádruplo
            Quadruple quad = new Quadruple(operator, address1, address2, addressTemp);
            quadruples.add(quad);
            System.out.println("DEBUG QUAD GENERADO: " + quadCounter + ": " + quad);
            quadCounter++;

            // Guardar el temporal en la pila de operandos
            operandStack.push(tempVar);
            typeStack.push(resultType);
        }
    }

    /**
     * Determina el tipo resultante de una operación.
     * Implementación simplificada.
     */
    private String determineResultType(String type1, String operator, String type2) {
        // Operadores relacionales siempre devuelven bool
        if (operator.equals(">") || operator.equals("<") || operator.equals("==") ||
            operator.equals("!=") || operator.equals(">=") || operator.equals("<=") ||
            operator.equals("&&") || operator.equals("||")) {
            return "bool";
        }

        // Si alguno es float, el resultado es float
        if (type1.equals("float") || type2.equals("float")) {
            return "float";
        }

        // Para operaciones aritméticas entre enteros
        return "int";
    }

    // ---  Salida de Cuádruplos  ---

    /**
     * Obtiene la lista de cuádruplos generados.
     * @return Lista de cuádruplos
     */
    public List<Quadruple> getQuadruples() {
        return new ArrayList<>(quadruples);
    }

    /**
     * Obtiene el mapa de constantes y sus valores.
     * @return Mapa de constantes
     */
    public Map<Integer, Object> getConstantValues() {
        return constantValues;
    }

    /**
     * Imprime los cuádruplos generados.
     */
    public void printQuadruples() {
        System.out.println("--- Cuádruplos ---");
        for (int i = 0; i < quadruples.size(); i++) {
            System.out.println(i + ": " + quadruples.get(i));
        }
    }
}