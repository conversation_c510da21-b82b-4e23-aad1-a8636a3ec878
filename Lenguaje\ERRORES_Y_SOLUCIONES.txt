===============================================================================
                    DOCUMENTACIÓN DE ERRORES Y SOLUCIONES
                         Compilador de Lenguaje - Java
===============================================================================

FECHA: Diciembre 2024
AUTOR: Documentación de correcciones realizadas

===============================================================================
                              RESUMEN EJECUTIVO
===============================================================================

Durante las pruebas del compilador se identificaron dos errores críticos
relacionados con el manejo de variables temporales y parámetros de función
en la máquina virtual. Ambos errores fueron corregidos exitosamente.

ERRORES CORREGIDOS:
1. Variables temporales imprimiendo NULL en ciclos (Fibonacci)
2. Conflicto de direcciones entre parámetros y variables locales en funciones

===============================================================================
                                ERROR #1
                    Variables temporales NULL en ciclos
===============================================================================

DESCRIPCIÓN DEL PROBLEMA:
- En el testcase de Fibonacci, las variables temporales estaban imprimiendo
  NULL en lugar de los valores calculados
- El cuádruplo de suma nunca se ejecutaba en los ciclos
- Esto causaba que las variables temporales no se inicializaran correctamente

CAUSA RAÍZ:
- Doble incremento del program counter en saltos condicionales
- En performGotoF() y performGotoT(), cuando la condición era verdadera
  (no saltaba), se incrementaba manualmente el program counter
- Luego executeQuadruple() incrementaba automáticamente el program counter
- Resultado: se saltaba el cuádruplo siguiente (operación aritmética)

ARCHIVOS AFECTADOS:
- VirtualMachine.java

CAMBIOS REALIZADOS:

1. Archivo: VirtualMachine.java - Método performGotoF()
   ANTES:
   ```java
   private void performGotoF(GeneradorCuadruplosVisitor.Quadruple quad) {
       Object condition = getMemoryValue(quad.arg1);
       if (condition instanceof Boolean && !(Boolean) condition) {
           programCounter = quad.result;
           return;
       }
       programCounter++; // ← PROBLEMA: Doble incremento
   }
   ```

   DESPUÉS:
   ```java
   private void performGotoF(GeneradorCuadruplosVisitor.Quadruple quad) {
       Object condition = getMemoryValue(quad.arg1);
       if (condition instanceof Boolean && !(Boolean) condition) {
           programCounter = quad.result;
           return;
       }
       // El incremento del PC se hace automáticamente en executeQuadruple()
   }
   ```

2. Archivo: VirtualMachine.java - Método performGotoT()
   ANTES:
   ```java
   private void performGotoT(GeneradorCuadruplosVisitor.Quadruple quad) {
       Object condition = getMemoryValue(quad.arg1);
       if (condition instanceof Boolean && (Boolean) condition) {
           programCounter = quad.result;
           return;
       }
       programCounter++; // ← PROBLEMA: Doble incremento
   }
   ```

   DESPUÉS:
   ```java
   private void performGotoT(GeneradorCuadruplosVisitor.Quadruple quad) {
       Object condition = getMemoryValue(quad.arg1);
       if (condition instanceof Boolean && (Boolean) condition) {
           programCounter = quad.result;
           return;
       }
       // El incremento del PC se hace automáticamente en executeQuadruple()
   }
   ```

RESULTADO:
- Los ciclos ahora ejecutan correctamente todas las operaciones aritméticas
- Las variables temporales se inicializan con los valores correctos
- El programa de Fibonacci produce la secuencia correcta: 0, 1, 1, 2, 3

===============================================================================
                                ERROR #2
              Conflicto de direcciones en parámetros de función
===============================================================================

DESCRIPCIÓN DEL PROBLEMA:
- Los parámetros de función se almacenaban correctamente en memoria
- Pero cuando se usaban dentro de la función, se asignaban nuevas direcciones
- Esto causaba que las operaciones aritméticas accedieran a direcciones vacías
- Resultado: RuntimeException por valores NULL en operaciones aritméticas

CAUSA RAÍZ:
- Los parámetros se procesaban y almacenaban en direcciones locales consecutivas
- Pero las variables locales también empezaban desde la misma dirección base
- No había separación entre el espacio de parámetros y variables locales
- getAddressForOperand() no reconocía que los parámetros ya tenían direcciones

ARCHIVOS AFECTADOS:
- VirtualMemory.java
- GeneradorCuadruplosVisitor.java

CAMBIOS REALIZADOS:

1. Archivo: VirtualMemory.java - Nuevos métodos para manejo de parámetros
   AGREGADO:
   ```java
   // Contador de parámetros para la función actual
   private int currentParameterCount = 0;

   public void setParameterCount(int paramCount) {
       this.currentParameterCount = paramCount;
       // Ajustar contadores locales para empezar después de parámetros
       localIntCounter = LOCAL_INT_START + paramCount;
       localFloatCounter = LOCAL_FLOAT_START + paramCount;
       localStringCounter = LOCAL_STRING_START + paramCount;
   }

   public int getParameterCount() {
       return currentParameterCount;
   }
   ```

2. Archivo: VirtualMemory.java - Método resetLocalAndTemp()
   ANTES:
   ```java
   public void resetLocalAndTemp() {
       localIntCounter = LOCAL_INT_START;
       localFloatCounter = LOCAL_FLOAT_START;
       localStringCounter = LOCAL_STRING_START;
       // ... otros contadores
   }
   ```

   DESPUÉS:
   ```java
   public void resetLocalAndTemp() {
       localIntCounter = LOCAL_INT_START;
       localFloatCounter = LOCAL_FLOAT_START;
       localStringCounter = LOCAL_STRING_START;
       // ... otros contadores
       currentParameterCount = 0; // ← AGREGADO
   }
   ```

3. Archivo: GeneradorCuadruplosVisitor.java - Nuevas variables de control
   AGREGADO:
   ```java
   // Flag para rastrear si estamos dentro de una función
   private boolean insideFunction = false;

   // Contador para asignar direcciones consecutivas a parámetros
   private int currentParameterIndex = 0;
   ```

4. Archivo: GeneradorCuadruplosVisitor.java - Método getAddressForOperand()
   ANTES:
   ```java
   } else {
       // Variable
       boolean isGlobal = true; // ← PROBLEMA: Siempre global
       address = memory.getVariableAddress(operand, type, isGlobal);
   }
   ```

   DESPUÉS:
   ```java
   } else {
       // Variable - determinar si es global o local basándose en el contexto
       boolean isGlobal = !insideFunction; // ← CORREGIDO
       address = memory.getVariableAddress(operand, type, isGlobal);
   }

5. Archivo: GeneradorCuadruplosVisitor.java - Método visitFuncs()
   AGREGADO al inicio del procesamiento de función:
   ```java
   // Marcar que estamos dentro de una función
   insideFunction = true;

   // Contar parámetros antes de procesarlos
   int paramCount = countParameters(ctx.atributo());
   memory.setParameterCount(paramCount);

   // Resetear contador de parámetros para asignación manual
   currentParameterIndex = 0;
   ```

   AGREGADO al final del procesamiento de función:
   ```java
   // Marcar que ya no estamos dentro de una función
   insideFunction = false;
   ```

6. Archivo: GeneradorCuadruplosVisitor.java - Método visitAtributo()
   ANTES:
   ```java
   public String visitAtributo(LenguajeParser.AtributoContext ctx) {
       if (ctx.ID() != null) {
           String paramName = ctx.ID().getText();
           String paramType = ctx.type().getText();

           // Agregar a tabla de símbolos
           Map<String, SemanticData.Variable> localTable = symbolTableStack.peek();
           localTable.put(paramName, new SemanticData.Variable(paramName, paramType));

           // Procesar más parámetros...
       }
   }
   ```

   DESPUÉS:
   ```java
   public String visitAtributo(LenguajeParser.AtributoContext ctx) {
       if (ctx.ID() != null) {
           String paramName = ctx.ID().getText();
           String paramType = ctx.type().getText();

           // Agregar a tabla de símbolos
           Map<String, SemanticData.Variable> localTable = symbolTableStack.peek();
           localTable.put(paramName, new SemanticData.Variable(paramName, paramType));

           // ← AGREGADO: Asignar dirección específica al parámetro
           int paramAddress = VirtualMemory.LOCAL_INT_START + currentParameterIndex;
           operandToAddress.put(paramName, paramAddress);
           currentParameterIndex++;

           // Procesar más parámetros...
       }
   }
   ```

7. Archivo: GeneradorCuadruplosVisitor.java - Nuevos métodos de utilidad
   AGREGADO:
   ```java
   private int countParameters(LenguajeParser.AtributoContext ctx) {
       if (ctx == null || ctx.ID() == null) return 0;

       int count = 1; // Este parámetro

       // Contar parámetros adicionales recursivamente
       if (ctx.atr_opt() != null) {
           count += countParametersInAtrOpt(ctx.atr_opt());
       }

       return count;
   }

   private int countParametersInAtrOpt(LenguajeParser.Atr_optContext ctx) {
       if (ctx == null || ctx.COMMA() == null) return 0;

       int count = 0;
       if (ctx.atributo() != null) {
           count += countParameters(ctx.atributo());
       }

       return count;
   }
   ```

CORRECCIONES ADICIONALES PARA LLAMADAS A FUNCIÓN:

8. Archivo: VirtualMachine.java - Método performGosub()
   ANTES:
   ```java
   // Hacer push del registro de activación
   callStack.push(pendingActivationRecord);
   pendingActivationRecord = null;

   // Saltar a la función
   programCounter = quad.arg1-1; // ← PROBLEMA: -1 incorrecto
   ```

   DESPUÉS:
   ```java
   // Establecer la dirección de retorno correcta (después del GOSUB)
   pendingActivationRecord.setReturnAddress(programCounter + 1);

   // Hacer push del registro de activación
   callStack.push(pendingActivationRecord);
   pendingActivationRecord = null;

   // Saltar a la función
   programCounter = quad.arg1; // ← CORREGIDO: sin -1
   return; // ← AGREGADO: No incrementar PC automáticamente
   ```

9. Archivo: VirtualMachine.java - Método performERA()
   ANTES:
   ```java
   // Crear el ActivationRecord y guardarlo para GOSUB
   pendingActivationRecord = new ActivationRecord(functionName, programCounter + 1, baseAddress);
   ```

   DESPUÉS:
   ```java
   // Crear el ActivationRecord y guardarlo para GOSUB
   // La dirección de retorno se establecerá en GOSUB
   pendingActivationRecord = new ActivationRecord(functionName, -1, baseAddress);
   ```

10. Archivo: VirtualMachine.java - Método performEndFunc()
    ANTES:
    ```java
    private void performEndFunc() {
        if (!callStack.isEmpty()) {
            ActivationRecord record = callStack.pop();
            programCounter = record.getReturnAddress();
        } else {
            running = false;
        }
    }
    ```

    DESPUÉS:
    ```java
    private void performEndFunc() {
        if (!callStack.isEmpty()) {
            ActivationRecord record = callStack.pop();
            programCounter = record.getReturnAddress();
            return; // ← AGREGADO: No incrementar PC automáticamente
        } else {
            running = false;
        }
    }
    ```

11. Archivo: VirtualMachine.java - Método executeQuadruple()
    CAMBIOS en casos específicos:
    ```java
    case "GOSUB":
        performGosub(quad);
        return; // ← AGREGADO: No incrementar PC automáticamente

    case "ENDFUNC":
        performEndFunc();
        return; // ← AGREGADO: No incrementar PC automáticamente
    ```

RESULTADO:
- Los parámetros de función ahora se asignan a direcciones específicas (4000, 4001, etc.)
- Las variables locales empiezan después de los parámetros (4002 en adelante)
- No hay conflicto entre parámetros y variables locales
- Las operaciones aritméticas acceden a los valores correctos
- Las llamadas a función y retornos funcionan correctamente

===============================================================================
                            PRUEBAS REALIZADAS
===============================================================================

TESTCASE FIBONACCI (fibonnachi.Lenguaje):
- ANTES: Variables temporales imprimían NULL
- DESPUÉS: Secuencia correcta 0, 1, 1, 2, 3

TESTCASE 7 (testcase7.Lenguaje):
- ANTES: RuntimeException por valores NULL en operaciones aritméticas
- DESPUÉS: Salida correcta 5, 3, 8 (parámetros a=5, b=3, resultado=8)

===============================================================================
                              LECCIONES APRENDIDAS
===============================================================================

1. MANEJO DE PROGRAM COUNTER:
   - Los métodos de salto no deben incrementar manualmente el PC
   - executeQuadruple() maneja automáticamente el incremento
   - Usar 'return' para evitar doble incremento

2. GESTIÓN DE MEMORIA EN FUNCIONES:
   - Separar claramente espacios de parámetros y variables locales
   - Asignar direcciones específicas a parámetros durante el parsing
   - Ajustar contadores de memoria según el número de parámetros

3. DEBUGGING:
   - El modo debug fue crucial para identificar los problemas
   - Rastrear la ejecución cuádruplo por cuádruplo reveló los saltos incorrectos
   - Verificar el estado de memoria en cada paso

4. ARQUITECTURA:
   - La separación entre generación de cuádruplos y ejecución es correcta
   - El sistema de direcciones virtuales funciona bien cuando se configura correctamente
   - La pila de registros de activación maneja correctamente las llamadas anidadas

===============================================================================
                                CONCLUSIÓN
===============================================================================

Ambos errores fueron causados por problemas en el manejo de flujo de control
y gestión de memoria. Las correcciones implementadas:

1. Eliminaron el doble incremento del program counter en saltos condicionales
2. Implementaron un sistema robusto de asignación de direcciones para parámetros
3. Separaron correctamente los espacios de memoria de parámetros y variables locales
4. Corrigieron el flujo de llamadas y retornos de función

El compilador ahora maneja correctamente:
- Ciclos con variables temporales
- Funciones con parámetros
- Operaciones aritméticas en contextos locales
- Llamadas y retornos de función

ESTADO FINAL: ✅ TODOS LOS ERRORES CORREGIDOS Y VERIFICADOS

===============================================================================
