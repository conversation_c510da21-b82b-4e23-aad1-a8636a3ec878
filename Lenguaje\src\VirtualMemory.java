import java.util.HashMap;
import java.util.Map;

/**
 * Clase que administra la memoria virtual para el compilador.
 * Asigna direcciones virtuales a variables, constantes y temporales.
 */
public class VirtualMemory {
    // Rangos de direcciones para cada tipo y ámbito
    public static final int GLOBAL_INT_START = 1000;
    public static final int GLOBAL_FLOAT_START = 2000;
    public static final int GLOBAL_STRING_START = 3000;
    public static final int LOCAL_INT_START = 4000;
    public static final int LOCAL_FLOAT_START = 5000;
    public static final int LOCAL_STRING_START = 6000;
    public static final int TEMP_INT_START = 7000;
    public static final int TEMP_FLOAT_START = 8000;
    public static final int TEMP_STRING_START = 9000;
    public static final int TEMP_BOOL_START = 10000;
    public static final int CTE_INT_START = 11000;
    public static final int CTE_FLOAT_START = 12000;
    public static final int CTE_STRING_START = 13000;

    // Contadores para cada segmento
    private int globalIntCounter = GLOBAL_INT_START;
    private int globalFloatCounter = GLOBAL_FLOAT_START;
    private int globalStringCounter = GLOBAL_STRING_START;
    private int localIntCounter = LOCAL_INT_START;
    private int localFloatCounter = LOCAL_FLOAT_START;
    private int localStringCounter = LOCAL_STRING_START;
    private int tempIntCounter = TEMP_INT_START;
    private int tempFloatCounter = TEMP_FLOAT_START;
    private int tempStringCounter = TEMP_STRING_START;
    private int tempBoolCounter = TEMP_BOOL_START;
    private int cteIntCounter = CTE_INT_START;
    private int cteFloatCounter = CTE_FLOAT_START;
    private int cteStringCounter = CTE_STRING_START;

    // Tablas para almacenar direcciones
    private Map<String, Integer> variableAddresses = new HashMap<>();
    private Map<String, Integer> constantAddresses = new HashMap<>();
    private Map<String, Integer> tempAddresses = new HashMap<>();

    /**
     * Obtiene la dirección virtual para una variable.
     * @param name Nombre de la variable
     * @param type Tipo de la variable
     * @param isGlobal Indica si la variable es global
     * @return Dirección virtual asignada
     */
    public int getVariableAddress(String name, String type, boolean isGlobal) {
        if (variableAddresses.containsKey(name)) {
            return variableAddresses.get(name);
        }

        // Si el tipo es desconocido, asumimos int por defecto
        if (type == null || type.equals("unknown")) {
            System.out.println("Advertencia: Tipo desconocido para la variable '" + name + "'. Asumiendo tipo 'int'.");
            type = "int";
        }

        int address;
        if (isGlobal) {
            switch (type) {
                case "int": address = globalIntCounter++; break;
                case "float": address = globalFloatCounter++; break;
                case "str": address = globalStringCounter++; break;
                case "bool": address = globalIntCounter++; break; // Usamos int para booleanos
                default:
                    System.out.println("Advertencia: Tipo no estándar '" + type + "' para la variable '" + name + "'. Asumiendo tipo 'int'.");
                    address = globalIntCounter++;
                    break;
            }
        } else {
            switch (type) {
                case "int": address = localIntCounter++; break;
                case "float": address = localFloatCounter++; break;
                case "str": address = localStringCounter++; break;
                case "bool": address = localIntCounter++; break; // Usamos int para booleanos
                default:
                    System.out.println("Advertencia: Tipo no estándar '" + type + "' para la variable '" + name + "'. Asumiendo tipo 'int'.");
                    address = localIntCounter++;
                    break;
            }
        }

        variableAddresses.put(name, address);
        return address;
    }

    /**
     * Obtiene la dirección virtual para una constante.
     * @param value Valor de la constante
     * @param type Tipo de la constante
     * @return Dirección virtual asignada
     */
    public int getConstantAddress(String value, String type) {
        if (constantAddresses.containsKey(value)) {
            return constantAddresses.get(value);
        }

        // Si el tipo es desconocido, intentamos inferirlo del valor
        if (type == null || type.equals("unknown")) {
            if (value.matches("\\d+")) {
                System.out.println("Advertencia: Tipo desconocido para la constante '" + value + "'. Inferido tipo 'int'.");
                type = "int";
            } else if (value.matches("\\d+\\.\\d+")) {
                System.out.println("Advertencia: Tipo desconocido para la constante '" + value + "'. Inferido tipo 'float'.");
                type = "float";
            } else if (value.startsWith("\"") && value.endsWith("\"")) {
                System.out.println("Advertencia: Tipo desconocido para la constante '" + value + "'. Inferido tipo 'str'.");
                type = "str";
            } else if (value.equals("true") || value.equals("false")) {
                System.out.println("Advertencia: Tipo desconocido para la constante '" + value + "'. Inferido tipo 'bool'.");
                type = "bool";
            } else {
                System.out.println("Advertencia: Tipo desconocido para la constante '" + value + "'. Asumiendo tipo 'int'.");
                type = "int";
            }
        }

        int address;
        switch (type) {
            case "int": address = cteIntCounter++; break;
            case "float": address = cteFloatCounter++; break;
            case "str": address = cteStringCounter++; break;
            case "bool": address = cteIntCounter++; break; // Usamos int para booleanos
            default:
                System.out.println("Advertencia: Tipo no estándar '" + type + "' para la constante '" + value + "'. Asumiendo tipo 'int'.");
                address = cteIntCounter++;
                break;
        }

        constantAddresses.put(value, address);
        return address;
    }

    /**
     * Obtiene la dirección virtual para una variable temporal.
     * @param type Tipo de la variable temporal
     * @return Dirección virtual asignada
     */
    public int getTempAddress(String type) {
        String tempName = "t" + tempAddresses.size();

        // Si el tipo es desconocido, asumimos int por defecto
        if (type == null || type.equals("unknown")) {
            System.out.println("Advertencia: Tipo desconocido para la variable temporal '" + tempName + "'. Asumiendo tipo 'int'.");
            type = "int";
        }

        int address;
        switch (type) {
            case "int": address = tempIntCounter++; break;
            case "float": address = tempFloatCounter++; break;
            case "str": address = tempStringCounter++; break;
            case "bool": address = tempBoolCounter++; break;
            default:
                System.out.println("Advertencia: Tipo no estándar '" + type + "' para la variable temporal '" + tempName + "'. Asumiendo tipo 'int'.");
                address = tempIntCounter++;
                break;
        }

        tempAddresses.put(tempName, address);
        return address;
    }

    /**
     * Obtiene la dirección virtual para una variable temporal específica.
     * @param tempName Nombre de la variable temporal
     * @param type Tipo de la variable temporal
     * @return Dirección virtual asignada
     */
    public int getTempAddressForName(String tempName, String type) {
        if (tempAddresses.containsKey(tempName)) {
            return tempAddresses.get(tempName);
        }

        // Si el tipo es desconocido, asumimos int por defecto
        if (type == null || type.equals("unknown")) {
            System.out.println("Advertencia: Tipo desconocido para la variable temporal '" + tempName + "'. Asumiendo tipo 'int'.");
            type = "int";
        }

        int address;
        switch (type) {
            case "int": address = tempIntCounter++; break;
            case "float": address = tempFloatCounter++; break;
            case "str": address = tempStringCounter++; break;
            case "bool": address = tempBoolCounter++; break;
            default:
                System.out.println("Advertencia: Tipo no estándar '" + type + "' para la variable temporal '" + tempName + "'. Asumiendo tipo 'int'.");
                address = tempIntCounter++;
                break;
        }

        tempAddresses.put(tempName, address);
        return address;
    }

    /**
     * Resetea los contadores locales y temporales.
     * Útil al cambiar de contexto (por ejemplo, al entrar/salir de una función).
     */
    public void resetLocalAndTemp() {
        localIntCounter = LOCAL_INT_START;
        localFloatCounter = LOCAL_FLOAT_START;
        localStringCounter = LOCAL_STRING_START;
        tempIntCounter = TEMP_INT_START;
        tempFloatCounter = TEMP_FLOAT_START;
        tempStringCounter = TEMP_STRING_START;
        tempBoolCounter = TEMP_BOOL_START;
    }
}
